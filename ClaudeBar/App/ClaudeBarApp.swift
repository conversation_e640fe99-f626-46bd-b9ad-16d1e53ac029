import SwiftUI

@main
struct ClaudeBarApp: App {
    @NSApplicationDelegateAdaptor(AppDelegate.self) var appDelegate
    @StateObject private var appState = AppState()
    
    var body: some Scene {
        // 隐藏的主窗口，仅用于初始化 appState
        WindowGroup {
            EmptyView()
                .onAppear {
                    // 将 appState 传递给 AppDelegate
                    appDelegate.appState = appState
                    // 隐藏窗口
                    if let window = NSApp.windows.first {
                        window.orderOut(nil)
                    }
                }
        }
        .windowStyle(.hiddenTitleBar)
        .windowResizability(.contentSize)
        .defaultSize(width: 0, height: 0)
        
        // 设置窗口（如果需要的话）
        Settings {
            EmptyView()
        }
        .windowResizability(.contentSize)
        .windowStyle(.hiddenTitleBar)
        .defaultSize(width: 0, height: 0)
    }
}