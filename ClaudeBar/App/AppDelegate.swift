import AppKit
import Swift<PERSON>

@MainActor
class AppDelegate: NSObject, NSApplicationDelegate {
    var appState: AppState! {
        didSet {
            if appState != nil && statusItemManager == nil {
                setupStatusItemManager()
                loadConfigs()
            }
        }
    }
    private var statusItemManager: StatusItemManager?
    
    func applicationDidFinishLaunching(_ notification: Notification) {
        // 设置为菜单栏应用，不在 Dock 显示
        NSApp.setActivationPolicy(.accessory)
        
        // AppState 会由主应用传入，不在这里初始化
        // 如果已经有 appState，则立即设置
        if appState != nil {
            setupStatusItemManager()
            loadConfigs()
        }
    }
    
    private func setupStatusItemManager() {
        guard appState != nil else { return }
        statusItemManager = StatusItemManager(appState: appState)
    }
    
    private func loadConfigs() {
        guard appState != nil else { return }
        Task {
            await appState.loadConfigs()
        }
    }
    
    func applicationShouldTerminateAfterLastWindowClosed(_ sender: NSApplication) -> Bool {
        // 防止关闭最后一个窗口时应用退出
        // 菜单栏应用应该继续运行在后台
        return false
    }
    
    func applicationWillTerminate(_ notification: Notification) {
        statusItemManager = nil
    }
}